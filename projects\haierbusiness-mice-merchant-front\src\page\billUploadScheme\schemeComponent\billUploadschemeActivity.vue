<script setup lang="ts">
// 方案互动-拓展方案
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits } from 'vue';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';

const props = defineProps({
  schemeItem: {
    type: Object,
    default: {},
  },
  schemeCacheItem: {
    type: Object,
    default: {},
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  schemeIndex: {
    type: Number,
    default: 0,
  },
  schemeType: {
    // 方案提报类型 // 查看需求-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload
    type: String,
    default: '',
  },
  showBindingScheme: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(['schemePriceEmit', 'schemeActivityEmit']);

const oldSchemeList = ref<Array<any>>([]);
const newSchemeList = ref<Array<any>>([]);

const subtotal = ref<number>(0); // 小计

// 价格计算
const priceCalcFun = () => {
  const isAllPriceWrite = newSchemeList.value.every((e) => e.planPrice && e.planPrice > 0);
  subtotal.value = 0;

  if (isAllPriceWrite) {
    newSchemeList.value.forEach((e) => {
      subtotal.value += e.planPrice;
    });

    emit('schemePriceEmit', { type: 'activity', totalPrice: subtotal.value, schemeIndex: props.schemeIndex });
  }
};

watch(
  () => props.schemeItem,
  (newObj) => {
    // 添加详细的调试信息
    console.log('%c [ billUploadschemeActivity - 拓展方案数据调试 ]', 'font-size:13px; background:pink; color:#bf2c9f;', newObj);

    oldSchemeList.value = JSON.parse(JSON.stringify(newObj))?.activities || [];

    if (props.isSchemeCache && props.schemeCacheItem && props.schemeCacheItem.activities && props.schemeCacheItem.activities.length > 0) {
      // 缓存 - 反显（只有当缓存数据存在且不为空时才使用）
      newSchemeList.value = props.schemeCacheItem.activities;

      // 价格计算
      priceCalcFun();
    } else {
      // 使用原始数据或缓存为空时的回退逻辑
      const demandData = JSON.parse(JSON.stringify(newObj))?.activities || [];

      newSchemeList.value = demandData.map((e) => {
        return {
          miceDemandActivityId: e.id,

          demandDate: e.demandDate,
          schemePersonNum: e.personNum,

          paths: e.paths,
          fileList: e.fileList,

          description: e.description,

          demandUnitPrice: e.demandUnitPrice,
          schemeUnitPrice: e.demandUnitPrice,
        };
      });

      console.log('映射后的 newSchemeList.value:', newSchemeList.value);

      // 如果是缓存模式但缓存为空，也需要计算价格
      if (props.isSchemeCache) {
        priceCalcFun();
      }
    }

    // 小计
    subtotal.value = 0;
    newSchemeList.value.forEach((e) => {
      if (e.schemeUnitPrice && e.schemePersonNum) {
        subtotal.value += e.schemeUnitPrice * e.schemePersonNum;
      }
    });

    emit('schemePriceEmit', { type: 'activity', totalPrice: subtotal.value, schemeIndex: props.schemeIndex });
  },
  {
    immediate: true,
    deep: true,
  },
);

const schemePlanLabelList = ['费用标准', '人数', '活动需求', '附件'];

const changePrice = (index: number) => {
  if (newSchemeList.value[index].biddingPrice) {
    newSchemeList.value[index].planPrice =
      newSchemeList.value[index].biddingPrice * newSchemeList.value[index].schemePersonNum;
  }

  // 价格计算
  priceCalcFun();
};

// const fileChange = (fileList: Array<any>) => {
//   let document = '';
//   let file = {};

//   fileList.forEach((item, index) => {
//     let isJson = true;

//     try {
//       file = JSON.parse(item);
//     } catch (error) {
//       console.log(error);
//       isJson = false;
//     }

//     if (!isJson) return;

//     document += `
//       <a target='_blank' href='${file.url}'>${file.name}</a>
//       <span style='margin-right: 10px;color: #86909c' >${index === fileList.length - 1 ? '' : ','}</span>
//     `;
//   });

//   return document ? document : '-';
// };

// 暂存
const activityTempSave = () => {
  emit('schemeActivityEmit', {
    schemeActivities: [...newSchemeList.value],
    schemeIndex: props.schemeIndex,
  });
};

// 校验
const activitySub = () => {
  let isVerPassed = true;

  // newSchemeList.value.forEach((e, i) => {
  //   if (!e.schemeUnitPrice) {
  //     message.error('请输入' + e.demandDate + '拓展方案' + (i + 1) + '竞价单价');

  //     isVerPassed = false;
  //     return;
  //   }
  // });

  if (isVerPassed) {
    activityTempSave();
  }

  return isVerPassed;
};

defineExpose({ activitySub, activityTempSave });

onMounted(async () => {});
</script>

<template>
  <!-- 拓展方案 -->
  <div class="scheme_vehicle">
    <div class="common_table">
      <!-- 左侧 -->
      <div class="common_table_l" v-if="props.schemeType !== 'notBidding' && props.schemeType !== 'biddingView'">
        <div class="scheme_plan_title">
          <div class="scheme_plan_img mr10"></div>
          <span>拓展需求</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in oldSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '拓展需求' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.demandUnitPrice ? item.demandUnitPrice + '元/人' : '-' }}
                </template>
                {{ item.demandUnitPrice ? item.demandUnitPrice + '元/人' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              {{ item.personNum ? item.personNum + '人' : '-' }}
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.description || '-' }}
                </template>
                {{ item.description || '-' }}
              </a-tooltip>
            </div>
            <!-- <div class="scheme_plan_value pl12 pr12" v-html="fileChange(item.paths)"></div> -->
          </div>
        </div>
      </div>

      <div class="common_table_divide"></div>

      <!-- 右侧 -->
      <div class="common_table_r">
        <div class="scheme_plan_title">
          <div class="scheme_plan_img mr10"></div>
          <span>拓展方案</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in newSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '拓展方案' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.schemeUnitPrice ? item.schemeUnitPrice + '元/人' : '-' }}
                </template>
                {{ item.schemeUnitPrice ? item.schemeUnitPrice + '元/人' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value p0">
              <div
                class="pl12"
                v-if="
                  props.schemeType === 'notBidding' ||
                  props.schemeType === 'biddingView' ||
                  props.schemeType === 'schemeView'
                "
              >
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
                  </template>
                  {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
                </a-tooltip>
              </div>
              <div class="pl12" v-else>
                <a-tooltip placement="topLeft">
                  <template #title v-if="item.schemePersonNum">
                    {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
                  </template>
                  <a-input-number
                    v-model:value="item.schemePersonNum"
                    @change="changePrice(idx)"
                    style="width: calc(100% - 30px)"
                    placeholder="人数"
                    :min="1"
                    :max="999"
                    :precision="0"
                    :bordered="false"
                    :controls="false"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.description || '-' }}
                </template>
                {{ item.description || '-' }}
              </a-tooltip>
            </div>
            <!-- <div class="scheme_plan_value pl12 pr12" v-html="fileChange(item.paths)"></div> -->
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  item.schemeUnitPrice && item.schemePersonNum
                    ? '¥' + formatNumberThousands(item.schemeUnitPrice * item.schemePersonNum)
                    : '-'
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.schemeUnitPrice && item.schemePersonNum">
                {{ item.schemePersonNum + '人*' + item.schemeUnitPrice + '(单价)' }}
              </div>
            </div>
          </div>
        </div>

        <div v-show="subtotal" class="scheme_plan_subtotal mt16">
          {{ '小计：¥' + formatNumberThousands(subtotal) }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_vehicle {
  .scheme_plan_img {
    background: url('@/assets/image/demand/demand_activity.png');
    background-repeat: no-repeat;
    background-size: 18px 18px;
    background-position: left center;
  }

  // 价格输入框样式 - 如果有的话
  .scheme_plan_price_value {
    :deep(.ant-input-number .ant-input-number-input) {
      height: 24px;
      padding: 0 5px;
      text-align: end;

      width: 84px;
      font-weight: 500;
      font-size: 14px;
      color: #1868db;
      text-align: right;
      border-bottom: 1px solid #4e5969;
    }
  }

  // 人数输入框样式，模仿服务人员组件
  .scheme_plan_value {
    :deep(.ant-input-number) {
      border: none;
      box-shadow: none;

      .ant-input-number-input {
        height: auto;
        padding: 0;
        text-align: left;
        width: 100%;
        font-weight: normal;
        font-size: 14px;
        color: #333;
        border-bottom: none;
        border: none;

        &::placeholder {
          color: #bfbfbf;
        }
      }

      &:hover .ant-input-number-input,
      &:focus .ant-input-number-input,
      &.ant-input-number-focused .ant-input-number-input {
        border: none;
        box-shadow: none;
      }
    }

    .scheme_plan_edit {
      margin-left: 5px;
      display: inline-flex;
      vertical-align: middle;

      width: 16px;
      height: 18px;
      background: url('@/assets/image/common/edit_gray.png');
      background-repeat: no-repeat;
      background-size: 16px 16px;
    }
  }

  .p0 {
    padding: 0 !important;
  }
}
</style>
