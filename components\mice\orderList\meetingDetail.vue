<script setup lang="ts">
import { Button as hButton, Modal as hModal } from 'ant-design-vue';
// 会议详情 - 组件
name: 'meetingDetail';
import { ref, onMounted, reactive, onUnmounted, defineProps, computed } from 'vue';
import { DownOutlined, UpOutlined } from '@ant-design/icons-vue';
import { miceBidManOrderListApi } from '@haierbusiness-front/apis';
import { CardItem } from '@haierbusiness-front/common-libs';
import { useRoute, useRouter } from 'vue-router';
import { resolveParam } from '@haierbusiness-front/utils';
import finalNode from '@haierbusiness-front/components/mice/orderList/finalNode.vue';
import orderLog from '@haierbusiness-front/components/mice/orderList/orderLog.vue';
import memorandum from '@haierbusiness-front/components/mice/orderList/memorandum.vue';


import advisors from './../advisors/index.vue';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';

const route = useRoute();
const router = useRouter();
const store = applicationStore();
const props = defineProps({
  type: {
    type: String,
    default: 'manage',
    // manage - 后台-订单列表
    // user - 用户端-订单列表
  },
});

type CardList = CardItem[][];
const rowCount = ref<number>(Math.floor(document.body.clientWidth / 300));
const selectOrder3 = ref<string>('0');

const previewSource = ref<string>('demandOne');
const showProcess = ref<boolean>(false);
const visible = ref<boolean>(false);
const memorandumvisible = ref<boolean>(false);
const confirmLoading = ref(false);
const handleOk = (num) => {
  if (num == 1) {
    visible.value = false
  } else {
    memorandumvisible.value = false
  }

}

const isViewLog = ref(false)

const permission = () => {
  console.log(store.loginUser.authorities,'store.loginUser.authorities');
  
  store.loginUser.authorities.forEach((item) => {
    if (item.authority == '211' || item.authority =='213') {
      isViewLog.value = true
    }
  });
}

const cardListO = reactive<CardItem[]>([
  {
    title: '提报',
    container: '用于经办人提报需求',
    person: '经办人',
    status: '需求',
    line: null,
    id: 0,
  },
  {
    title: '提报',
    container: '用于经办人提报需求',
    person: '经办人',
    status: '需求',
    line: null,
    id: 1,
  },
  {
    title: '提报',
    container: '用于经办人提报需求',
    person: '经办人',
    status: '需求',
    line: null,
    id: 2,
  },
  {
    title: '提报',
    container: '用于经办人提报需求',
    person: '经办人',
    status: '需求',
    line: null,
    id: 3,
  },
  {
    title: '提报',
    container: '用于经办人提报需求',
    person: '经办人',
    status: '需求',
    line: null,
    id: 4,
  },
  {
    title: '提报',
    container: '用于经办人提报需求',
    person: '经办人',
    status: '需求',
    line: null,
    id: 5,
  },
  {
    title: '提报',
    container: '用于经办人提报需求',
    person: '经办人',
    status: '需求',
    line: null,
    id: 6,
  },
  {
    title: '提报',
    container: '用于经办人提报需求',
    person: '经办人',
    status: '需求',
    line: null,
    id: 7,
  },
  {
    title: '提报',
    container: '用于经办人提报需求',
    person: '经办人',
    status: '需求',
    line: null,
    id: 8,
  },
  {
    title: '提报',
    container: '用于经办人提报需求',
    person: '经办人',
    status: '需求',
    line: null,
    id: 9,
  },
  {
    title: '提报',
    container: '用于经办人提报需求',
    person: '经办人',
    status: '需求',
    line: null,
    id: 10,
  },
  {
    title: '提报',
    container: '用于经办人提报需求',
    person: '经办人',
    status: '需求',
    line: null,
    id: 11,
  },
]);

const cardList = reactive<CardList>([]);
const hideBtn = ref<string>('');

const getLine = () => {
  cardListO.forEach((row, rowIndex) => {
    if (rowIndex > 0) {
      const startElement = document.getElementById(`card` + (rowIndex - 1));
      const endElement = document.getElementById(`card` + rowIndex);
      if (startElement && endElement && row.hide === undefined) {
        row.line = new LeaderLine(startElement, endElement, {
          color: '#52B4FF',
          size: 2,
        });
      }
    }
  });
};

const getList = async () => {
  const res = await miceBidManOrderListApi.userDetails({ miceId: route.query.miceId });
  console.log(res);
  const res1 = await miceBidManOrderListApi.processDetails({
    id: res.pdMainId,
    verId: res.pdVerId,
  });
  // res.forEach((item, index) => {
  //   cardListO.push({
  //     title: item.name,
  //     container: '用于经办人提报需求',
  //     person: '经办人',
  //     status: '需求',
  //     line: null,
  //     id: 11,
  //   });
  // });
  // cardListO=
};
// 查看流程节点
const viewProcess = () => {
  showProcess.value = !showProcess.value;
};

// 备忘录
const viewMemo = () => {
  memorandumvisible.value = true
};
// 日志
const viewLog = () => {
  // console.log('%c [ 日志 ]-161', 'font-size:13px; background:pink; color:#bf2c9f;');
  visible.value = true
};
// 驳回
const rejectBtn = () => {
  console.log('%c [ 驳回 ]-161', 'font-size:13px; background:pink; color:#bf2c9f;');
};
// 确认
const confirmBtn = () => {
  console.log('%c [ 确认 ]-161', 'font-size:13px; background:pink; color:#bf2c9f;');
};
// 返回
const backBtn = () => {
  router.go(-1);
};

onMounted(() => {
  const record = resolveParam(route.query.record);
  permission()
  hideBtn.value = record.hideBtn || '';

  // getList();
  window.onresize = () => {
    rowCount.value = Math.floor(document.body.clientWidth / 300);
  };

  if (cardListO.length % rowCount.value !== 0) {
    let n = cardListO.length % rowCount.value;
    while (n >= 0) {
      cardListO.push({
        id: cardListO.length,
        hide: true,
        title: '提报',
        container: '用于经办人提报需求',
        person: '经办人',
        status: '需求',
        line: null,
      });
      n--;
    }
  }

  for (let i = 0; i < cardListO.length; i += rowCount.value) {
    cardList.push(cardListO.slice(i, i + rowCount.value));
  }

  setTimeout(() => {
    getLine();
  }, 1000);
});
onUnmounted(() => {
  cardListO.forEach((row) => {
    if (row.line) {
      row.line.remove();
      row.line = null;
    }
  });
});
</script>
<template>
  <div class="container">
    <!-- 会议详情 -->
    <!-- 详情 -->
    <div class="">
      <advisors :preview-source="previewSource" :is-manage-page="props.type === 'manage'" :hide-footer="true">
        <template #header>
          <div v-if="hideBtn !== '1'">
            <h-button type="link" @click="viewProcess()">
              {{ showProcess ? '收起流程节点' : '查看流程节点' }}
              <DownOutlined v-if="showProcess" />
              <UpOutlined v-else />
            </h-button>
            <a-button style="margin-right: 10px" size="small" @click="viewMemo()">备忘录</a-button>
            <a-button v-if="isViewLog" type="primary" size="small" @click="viewLog()">日志</a-button>
          </div>
        </template>
        <template #processSlot>
          <!-- 流程信息 -->
          <div class="" v-if="props.type === 'manage' && showProcess">
            <finalNode :nodeId="route.query.record" :key="route.fullPath"></finalNode>
          </div>
        </template>
        <template #orderLogSlot>

        </template>
        <template #footer>
          <div v-if="hideBtn !== '1'" style="display: inline-block">
            <a-button style="margin-right: 10px" size="small" @click="backBtn()">返回</a-button>
            <!-- <a-button style="margin-right: 10px" size="small" @click="rejectBtn()">驳回</a-button>
            <a-button type="primary" size="small" @click="confirmBtn()">确认</a-button> -->
          </div>
        </template>
      </advisors>

    </div>
    <h-modal :visible="visible" width="70%" :confirmLoading="confirmLoading" @ok="handleOk(1)" @cancel="visible = false"
      title="日志">
      <orderLog :nodeId="route.query.record" />
      <template #footer>
        <h-button @click="visible = false">关闭</h-button>
      </template>
    </h-modal>
    <h-modal :visible="memorandumvisible" width="70%" :confirmLoading="confirmLoading" @ok="handleOk(2)"
      @cancel="memorandumvisible = false" title="备忘录">
      <memorandum :nodeId="route.query.record" />
      <template #footer>
        <h-button @click="memorandumvisible = false">关闭</h-button>
      </template>
    </h-modal>
  </div>
</template>
<style scoped lang="less">
.container {
  padding-top: 16px;
  background: #ffffff;
  width: 100%;
  height: 100%;

  ::v-deep .ant-divider-inner-text {
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    font-family: PingFangSC-regular;
  }

  ::v-deep .ant-radio-button-wrapper-checked {
    background: #409eff;
    color: white;
  }
}

.btn1 {
  padding-left: 40px;
}

.vertical {
  display: flex;
  align-items: center;
}

.rowFlex {
  display: flex;
  justify-content: space-between;
}

.card {
  width: 100%;
  height: 120px;
  padding: 10px 20px;
}

.cardItem {
  border: 1px solid #e9e9e9;
  padding: 10px;
  width: 200px;
  height: 100px;
}

.cardItemTitle {
  height: 30px;
  color: rgba(14, 6, 6, 0.85);
  font-size: 14px;
  font-family: PingFangSC-regular;
}

.cardItemCenter {
  height: 25px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  font-family: PingFangSC-regular;
}

.cardItemBottom {
  height: 25px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  font-family: PingFangSC-regular;

  ::v-deep .ant-btn {
    height: 20px;
    display: flex;
    align-items: center;
  }
}

.btn2 {
  text-align: right;
  height: 20px;
  width: 100%;
  color: rgba(0, 0, 0, 0.85);
  font-size: 10px;
  font-family: PingFangSC-regular;
}

.img1 {
  margin: 0 5px;
  display: inline-block;
  width: 10px;
  height: 10px;
}
</style>
